<script type="text/javascript" src="https://alcdn.msauth.net/browser/2.38.0/js/msal-browser.min.js"></script>


    <meta name="azure-function-url" content="{{ settings['AzureFunctionUrl'] | default: '' }}">
    <meta name="registration-function-key" content="{{ settings['Registration Function Key'] | default: '' }}">
    <meta name="invitation-function-key" content="{{ settings['Invitation Function Key'] | default: '' }}">
    <meta name="msal-client-id" content="{{ settings['MSALClientId'] | default: '' }}">
    <meta name="msal-tenant-id" content="{{ settings['MSALTenantId'] | default: '' }}">
    <meta name="application-name" content="{{ settings['ApplicationName'] | default: '' }}">
    <meta name="entra-tenant-domain" content="{{ settings['EntraTenantDomain'] | default: '' }}">

    <script>
      function getConfig() {
        const functionUrl = document.querySelector('meta[name="azure-function-url"]')?.content;
        const registrationFunctionKey = document.querySelector('meta[name="registration-function-key"]')?.content;
        const invitationFunctionKey = document.querySelector('meta[name="invitation-function-key"]')?.content;
        const msalClientId = document.querySelector('meta[name="msal-client-id"]')?.content;
        const msalTenantId = document.querySelector('meta[name="msal-tenant-id"]')?.content;
        const applicationName = document.querySelector('meta[name="application-name"]')?.content;
        const entraTenantDomain = document.querySelector('meta[name="entra-tenant-domain"]')?.content;

        return {
          functionUrl: functionUrl || null,
          registrationFunctionKey: registrationFunctionKey || null,
          invitationFunctionKey: invitationFunctionKey || null,
          msalClientId: msalClientId || null,
          msalTenantId: msalTenantId || null,
          applicationName: applicationName || null,
          entraTenantDomain: entraTenantDomain || null
        };
      }

      window.appConfig = getConfig();
    </script>

<div class="col-lg-12 columnBlockLayout">
<div class="row sectionBlockLayout text-start">
  <div class="container container-flex">
    <div class="container registration-container">
      <h2>Create Account</h2>
      <p>Fill out the form below to create your new account.</p>

      <!-- Unified Notification Container -->
      <div id="notificationContainer" class="notification-container"></div>

          <form id="registrationForm">
            <div class="form-group mb-3">
              <label for="email" class="form-label fw-bold">Email Address</label>
              <input
                type="email"
                id="email"
                required
                class="form-control"
                placeholder="Enter your email address"
              />
              <div class="invalid-feedback" id="emailError"></div>
            </div>

            <div class="form-group mb-3">
              <label for="firstName" class="form-label fw-bold">First Name</label>
              <input
                type="text"
                id="firstName"
                required
                class="form-control"
                placeholder="Enter your first name"
              />
              <div class="invalid-feedback" id="firstNameError"></div>
            </div>

            <div class="form-group mb-3">
              <label for="lastName" class="form-label fw-bold">Last Name</label>
              <input
                type="text"
                id="lastName"
                required
                class="form-control"
                placeholder="Enter your last name"
              />
              <div class="invalid-feedback" id="lastNameError"></div>
            </div>

            <div class="form-group mb-3">
              <label for="invitationCode" class="form-label fw-bold">Invitation Code</label>
              <input
                type="text"
                id="invitationCode"
                required
                class="form-control"
                placeholder="Enter your invitation code"
                maxlength="20"
              />
              <div class="form-text">
                Enter the invitation code you received in your invitation email.
              </div>
              <div class="invalid-feedback" id="invitationCodeError"></div>
            </div>

            <div class="form-group mb-3">
              <label for="password" class="form-label fw-bold">Password</label>
              <div class="input-group">
                <input
                  type="password"
                  id="password"
                  required
                  class="form-control"
                  placeholder="Enter your password"
                  autocomplete="new-password"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="togglePassword"
                  title="Toggle password visibility"
                  aria-label="Toggle password visibility"
                >
                  👁️
                </button>
              </div>
              <div class="form-text">
                Password must be at least 8 characters with uppercase,
                lowercase, number, and special character.
              </div>
              <div class="invalid-feedback" id="passwordError"></div>
            </div>

            <div class="form-group mb-3">
              <label for="confirmPassword" class="form-label fw-bold">Confirm Password</label>
              <div class="input-group">
                <input
                  type="password"
                  id="confirmPassword"
                  required
                  class="form-control"
                  placeholder="Confirm your password"
                  autocomplete="new-password"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  id="toggleConfirmPassword"
                  title="Toggle confirm password visibility"
                  aria-label="Toggle confirm password visibility"
                >
                  👁️
                </button>
              </div>
              <div class="invalid-feedback" id="confirmPasswordError"></div>
            </div>

            <div class="form-check mb-3">
              <input
                type="checkbox"
                id="termsAccepted"
                required
                class="form-check-input"
              />
              <label for="termsAccepted" class="form-check-label">
                I agree to the
                <a href="/terms" target="_blank">Terms and Conditions</a>
              </label>
              <div class="invalid-feedback" id="termsError"></div>
            </div>

            <button
              type="submit"
              id="registerButton"
              class="btn btn-primary w-100"
            >
              Create Account
            </button>
          </form>

      <div id="errorMessage" class="alert alert-danger mt-3 d-none"></div>
      <div id="successMessage" class="alert alert-success mt-3 d-none"></div>

      <div class="text-center mt-3">
        <p>Already have an account? <a href="/" class="btn btn-outline-primary">Sign in with your existing account</a></p>
      </div>
    </div>
  </div>
</div>
</div>

<script src="registration.js"></script>
</body>
</html>
