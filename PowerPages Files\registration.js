// Unified Notification System for Power Pages
const NotificationSystem = {
  config: { defaultTimeout: 5000, fadeInDuration: 300, fadeOutDuration: 300, autoHide: true, showIcons: true },
  icons: { success: '✅', error: '❌', warning: '⚠️', info: 'ℹ️', loading: '⏳' },

  show(type, message, options = {}) {
    const settings = { ...this.config, ...options };
    const container = this.getNotificationContainer(settings.containerId);
    if (!container) {
      console.warn('Notification container not found');
      return;
    }

    if (settings.clearExisting !== false) this.clear(container);

    const notification = this.createNotification(type, message, settings);
    container.appendChild(notification);
    notification.classList.add('notification-fade-in');

    if (settings.autoHide && settings.timeout > 0) {
      setTimeout(() => this.hide(notification), settings.timeout || this.config.defaultTimeout);
    }

    this.setAriaLiveRegion(notification, type);
    return notification;
  },

  showSuccess(message, options = {}) {
    return this.show('success', message, { ...options, timeout: options.timeout || 4000, title: options.title || 'Success' });
  },

  showError(message, options = {}) {
    return this.show('error', message, { ...options, timeout: options.timeout || 0, title: options.title || 'Error' });
  },

  showWarning(message, options = {}) {
    return this.show('warning', message, { ...options, timeout: options.timeout || 6000, title: options.title || 'Warning' });
  },

  showInfo(message, options = {}) {
    return this.show('info', message, { ...options, timeout: options.timeout || 5000, title: options.title || 'Information' });
  },

  showLoading(message, options = {}) {
    return this.show('loading', message, { ...options, timeout: 0, title: options.title || 'Loading', autoHide: false });
  },

  createNotification(type, message, settings) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
    notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

    let content = '';
    if (settings.showIcons && this.icons[type]) {
      content += `<div class="notification-icon"><i class="${this.icons[type]}" aria-hidden="true"></i></div>`;
    }

    content += '<div class="notification-content">';
    if (settings.title) {
      content += `<div class="notification-title">${this.escapeHtml(settings.title)}</div>`;
    }
    content += `<div class="notification-message">${this.escapeHtml(message)}</div></div>`;

    notification.innerHTML = content;
    if (settings.compact) notification.classList.add('notification-compact');
    return notification;
  },

  getNotificationContainer(containerId) {
    if (containerId) return document.getElementById(containerId);

    const commonIds = ['notificationContainer', 'messageContainer', 'alertContainer'];
    for (const id of commonIds) {
      const container = document.getElementById(id);
      if (container) return container;
    }

    const jquerySelectors = ['#errorMessage', '#successMessage', '#messageContent'];
    for (const selector of jquerySelectors) {
      const element = $(selector);
      if (element.length > 0) return element[0];
    }

    return this.createDefaultContainer();
  },

  createDefaultContainer() {
    const container = document.createElement('div');
    container.id = 'notificationContainer';
    container.className = 'notification-container';

    const mainContent = document.querySelector('.card-body, .container, main, body');
    if (mainContent) {
      mainContent.insertBefore(container, mainContent.firstChild);
      return container;
    }
    return null;
  },

  hide(notification) {
    if (!notification || !notification.parentNode) return;
    notification.style.transition = `opacity ${this.config.fadeOutDuration}ms ease`;
    notification.style.opacity = '0';
    setTimeout(() => {
      if (notification.parentNode) notification.parentNode.removeChild(notification);
    }, this.config.fadeOutDuration);
  },

  clear(container) {
    if (!container) container = this.getNotificationContainer();
    if (container) {
      const notifications = container.querySelectorAll('.notification, .message, .alert');
      notifications.forEach(notification => this.hide(notification));
    }
    $('#errorMessage, #successMessage').hide();
  },

  setAriaLiveRegion(notification, type) {
    if (type === 'error') {
      notification.setAttribute('aria-live', 'assertive');
      notification.setAttribute('role', 'alert');
    } else {
      notification.setAttribute('aria-live', 'polite');
      notification.setAttribute('role', 'status');
    }
  },

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  },

  // Consolidated error handling with preserved functionality
  handleError(error, context = {}) {
    console.error('Error in context:', context, error);
    if (error.status) return this.handleHttpError(error, context);

    const errorTypes = {
      fetch: () => error instanceof TypeError && error.message.includes('fetch'),
      timeout: () => error.name === 'AbortError' || error.message.includes('timeout'),
      config: () => error.message.includes('configuration') || error.message.includes('missing'),
      rateLimit: () => error.message.includes('rate limit') || error.message.includes('429'),
      auth: () => error.message.includes('401') || error.message.includes('unauthorized')
    };

    const errorMessages = {
      fetch: { msg: 'Network connection failed. Please check your internet connection and try again.', title: 'Connection Error' },
      timeout: { msg: 'Request timed out. Please check your connection and try again. If the problem persists, try refreshing the page.', title: 'Request Timeout' },
      config: { msg: 'System configuration error. Please contact support if this persists.', title: 'Configuration Error' },
      rateLimit: { msg: error.message, title: 'Rate Limit', timeout: 10000 },
      auth: { msg: 'Authentication failed. Please refresh the page and try again.', title: 'Authentication Error' }
    };

    for (const [type, check] of Object.entries(errorTypes)) {
      if (check()) {
        const { msg, title, timeout = 0 } = errorMessages[type];
        return type === 'rateLimit' ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
      }
    }

    return this.showError(error.message || 'An unexpected error occurred. Please try again.', { title: 'Error', timeout: 0 });
  }
};

const SecureConfig = {
  getFunctionUrl: (functionName = 'RegistrationService') => {
    const baseUrl = window.appConfig?.functionUrl;
    return baseUrl ? `${baseUrl}/api/${functionName}` : null;
  },

  getFunctionKey: functionName => {
    const keyMap = {
      InvitationService: window.appConfig?.invitationFunctionKey,
      RegistrationService: window.appConfig?.registrationFunctionKey,
      PasswordService: window.appConfig?.passwordFunctionKey
    };

    const functionKey = keyMap[functionName] || window.appConfig?.registrationFunctionKey;
    return (!functionKey || functionKey.includes('ERROR_MISSING')) ? null : functionKey;
  },

  buildSecureUrl: (functionName, operation) => {
    const baseUrl = SecureConfig.getFunctionUrl(functionName);
    const functionKey = SecureConfig.getFunctionKey(functionName);
    return (!baseUrl || !functionKey) ? null : `${baseUrl}?operation=${operation}&code=${functionKey}`;
  },

  getMSALConfig: () => ({
    clientId: window.appConfig?.msalClientId,
    tenantId: window.appConfig?.msalTenantId
  })
};

// Add remaining NotificationSystem methods
NotificationSystem.handleHttpError = function(error, context = {}) {
  const httpErrors = {
    400: { msg: 'Invalid request. Please check your input and try again.', title: 'Invalid Request' },
    401: { msg: 'Authentication required. Please refresh the page and try again.', title: 'Authentication Required' },
    403: { msg: 'Access denied. You do not have permission to perform this action.', title: 'Access Denied' },
    404: { msg: 'Service not found. Please contact support if this persists.', title: 'Service Unavailable' },
    429: { msg: 'Too many requests. Please wait a moment before trying again.', title: 'Rate Limit Exceeded', timeout: 15000 },
    500: { msg: 'A server error occurred. Please try again later or contact support.', title: 'Server Error' }
  };

  const status = error.status;

  if (status === 409) {
    const conflictMsg = error.message || 'A conflict occurred. Please review your input.';
    if (conflictMsg.toLowerCase().includes('email') &&
        (conflictMsg.toLowerCase().includes('exists') || conflictMsg.toLowerCase().includes('already') || conflictMsg.toLowerCase().includes('duplicate'))) {
      return this.showError('An account with this email address already exists. Please try signing in instead.', { title: 'Account Already Exists', timeout: 0 });
    }
    return this.showError(conflictMsg, { title: 'Conflict', timeout: 0 });
  }

  const errorInfo = httpErrors[status] || (status >= 500 ? httpErrors[500] : { msg: 'Request failed. Please try again or contact support if the problem persists.', title: 'Request Failed' });
  const { msg, title, timeout = 0 } = errorInfo;

  return status === 429 ? this.showWarning(msg, { title, timeout }) : this.showError(msg, { title, timeout });
};

NotificationSystem.validateConfiguration = function(requiredConfig = []) {
  const missing = requiredConfig.filter(key => !window.appConfig || !window.appConfig[key]);
  if (missing.length > 0) {
    this.showError(`Missing configuration: ${missing.join(', ')}. Please contact support.`, { title: 'Configuration Error', timeout: 0 });
    return false;
  }
  return true;
};

NotificationSystem.validateDOMElements = function(elementSelectors = []) {
  const missing = elementSelectors.filter(selector => !document.querySelector(selector));
  if (missing.length > 0) {
    console.error('Missing DOM elements:', missing);
    this.showError('Page elements missing. Please refresh the page.', { title: 'Page Error', timeout: 0 });
    return false;
  }
  return true;
};

NotificationSystem.checkBrowserCompatibility = function() {
  const features = [
    { check: () => typeof fetch === 'undefined', name: 'Fetch API' },
    { check: () => typeof Promise === 'undefined', name: 'Promise support' },
    { check: () => typeof URLSearchParams === 'undefined', name: 'URL Parameters' },
    { check: () => !document.querySelector || !document.querySelectorAll, name: 'Modern DOM methods' }
  ];

  try {
    if (typeof localStorage === 'undefined' || typeof sessionStorage === 'undefined') {
      features.push({ check: () => true, name: 'Local Storage' });
    }
  } catch (e) {
    features.push({ check: () => true, name: 'Local Storage' });
  }

  const missing = features.filter(f => f.check()).map(f => f.name);

  if (missing.length > 0) {
    const message = `Your browser is missing required features: ${missing.join(', ')}. Please update to a modern browser (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+) to use this application.`;
    try {
      this.showError(message, { title: 'Browser Compatibility Issue', timeout: 0 });
    } catch (e) {
      alert('Browser Compatibility Issue: ' + message);
    }
    return false;
  }
  return true;
};

const { clientId: MSAL_CLIENT_ID, tenantId: MSAL_TENANT_ID } = SecureConfig.getMSALConfig();
const MSAL_REDIRECT_URI = window.location.origin;
const APPLICATION_NAME = window.appConfig?.applicationName || "ApplicationNameNotSet";

const InputSanitizer = {
  sanitizeString: input => typeof input === 'string' ? input.trim().replace(/[<>"'&]/g, '').substring(0, 256) : '',
  sanitizeEmail: input => typeof input === 'string' ? input.trim().toLowerCase().replace(/[<>"'&]/g, '').substring(0, 256) : '',

  validatePassword: password => {
    if (!password || typeof password !== 'string') return false;
    if (password.length < 8 || password.length > 128) return false;
    return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]).{8,}$/.test(password);
  },

  validateEmail: email => {
    if (!email || typeof email !== 'string') return false;
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim());
  },

  validateName: name => {
    if (!name || typeof name !== 'string') return false;
    const trimmed = name.trim();
    if (trimmed.length < 1 || trimmed.length > 50) return false;

    const namePattern = /^[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff\s'-]+$/;
    if (!namePattern.test(trimmed)) return false;
    if (!/[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff]/.test(trimmed)) return false;
    if (/[-']{3,}/.test(trimmed)) return false;
    if (/^[-'\s]|[-'\s]$/.test(trimmed)) return false;

    return true;
  },

  validateNameWithFeedback: (name, fieldName) => {
    if (!name || typeof name !== 'string') return { valid: false, message: `${fieldName} is required` };

    const trimmed = name.trim();
    if (trimmed.length < 1) return { valid: false, message: `${fieldName} is required` };
    if (trimmed.length > 50) return { valid: false, message: `${fieldName} must be 50 characters or less` };

    const namePattern = /^[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff\s'-]+$/;
    if (!namePattern.test(trimmed)) return { valid: false, message: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes` };
    if (!/[a-zA-ZÀ-ÿĀ-žА-я\u4e00-\u9fff]/.test(trimmed)) return { valid: false, message: `${fieldName} must contain at least one letter` };
    if (/[-']{3,}/.test(trimmed)) return { valid: false, message: `${fieldName} cannot contain more than 2 consecutive special characters` };
    if (/^[-'\s]|[-'\s]$/.test(trimmed)) return { valid: false, message: `${fieldName} cannot start or end with special characters` };

    return { valid: true, message: '' };
  }
};

const errMsgDiv = $('#errorMessage');
const successMsgDiv = $('#successMessage');
const registerBtn = $('#registerButton');
const regForm = $('#registrationForm');
const regFieldsDiv = $('#registrationFields');
const toggleBtns = {
  password: $('#togglePassword'),
  confirm: $('#toggleConfirmPassword')
};

const msalConfig = {
  auth: {
    clientId: MSAL_CLIENT_ID,
    authority: `https://login.microsoftonline.com/${MSAL_TENANT_ID}`,
    redirectUri: MSAL_REDIRECT_URI
  },
  cache: {
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: false
  }
};

const msalInstance = new msal.PublicClientApplication(msalConfig);

msalInstance.handleRedirectPromise()
  .then(tokenResponse => {
    if (tokenResponse) {
      msalInstance.setActiveAccount(tokenResponse.account);
    }
  })
  .catch(error => {
    handleMSALError(error);
  });

const handleMSALError = error => {
  if (!error.errorCode) {
    NotificationSystem.showError('Authentication system error. Please try again or contact support.', { title: 'Authentication Error', timeout: 0 });
    return;
  }

  const msalErrors = {
    user_cancelled: { msg: 'Sign-in was cancelled. Please try again.', title: 'Sign-in Cancelled', timeout: 5000, type: 'warning' },
    consent_required: { msg: 'Additional permissions required. Please contact support.', title: 'Permissions Required' },
    interaction_required: { msg: 'Authentication interaction required. Please try signing in again.', title: 'Authentication Required' },
    login_required: { msg: 'Please sign in to continue.', title: 'Sign-in Required' },
    network_error: { msg: 'Network error during authentication. Please check your connection and try again.', title: 'Network Error' }
  };

  const errorInfo = msalErrors[error.errorCode] || {
    msg: `Authentication failed: ${error.errorMessage || error.message || 'Unknown error'}`,
    title: 'Authentication Error'
  };

  const { msg, title, timeout = 0, type = 'error' } = errorInfo;
  NotificationSystem[type === 'warning' ? 'showWarning' : 'showError'](msg, { title, timeout });
};

// Unified notification functions
const showMessage = (message, isError = true, timeout = 0) => {
  const type = isError ? 'error' : 'success';
  const options = timeout > 0 ? { timeout } : {};
  NotificationSystem.show(type, message, options);

  errMsgDiv.add(successMsgDiv).addClass('d-none');
  const messageDiv = isError ? errMsgDiv : successMsgDiv;
  messageDiv.html(message).removeClass('d-none');
  if (timeout > 0) setTimeout(() => messageDiv.addClass('d-none'), timeout);
};

const showLoadingState = message => {
  NotificationSystem.showLoading(message);
  registerBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + message);
};

const resetLoadingState = () => {
  NotificationSystem.clear();
  registerBtn.prop('disabled', false).text('Create Account');
};

const showSuccess = (message, timeout = 4000) => NotificationSystem.showSuccess(message, { timeout });
const showError = message => NotificationSystem.showError(message);

const clearMessages = () => {
  NotificationSystem.clear();
  errMsgDiv.add(successMsgDiv).addClass('d-none');
};

const validatePasswordComplexity = password => {
  if (!password || password.length < 8) return false;
  return /[A-Z]/.test(password) && /[a-z]/.test(password) && /\d/.test(password) && /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(password);
};

const togglePasswordVisibility = (inputId, toggleButton) => {
  const input = $(`#${inputId}`);
  const icon = toggleButton.find('i');

  if (input.attr('type') === 'password') {
    input.attr('type', 'text');
    icon.removeClass('fa-eye').addClass('fa-eye-slash');
  } else {
    input.attr('type', 'password');
    icon.removeClass('fa-eye-slash').addClass('fa-eye');
  }
};

async function registerUser(userData) {
  showLoadingState('Creating Account...');
  try {
    const sanitizedData = {
      email: InputSanitizer.sanitizeEmail(userData.email),
      password: InputSanitizer.sanitizeString(userData.password),
      firstName: InputSanitizer.sanitizeString(userData.firstName),
      lastName: InputSanitizer.sanitizeString(userData.lastName),
      invitationCode: InputSanitizer.sanitizeString(userData.invitationCode || '')
    };

    if (!InputSanitizer.validateEmail(sanitizedData.email)) {
      throw new Error('Invalid email format');
    }

    if (!InputSanitizer.validatePassword(sanitizedData.password)) {
      throw new Error('Password does not meet security requirements');
    }

    if (!InputSanitizer.validateName(sanitizedData.firstName)) {
      throw new Error('Invalid first name');
    }

    if (!InputSanitizer.validateName(sanitizedData.lastName)) {
      throw new Error('Invalid last name');
    }

    if (!sanitizedData.invitationCode || sanitizedData.invitationCode.length < 6) {
      throw new Error('Valid invitation code is required');
    }

    const secureUrl = SecureConfig.buildSecureUrl('RegistrationService', 'register');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        Email: sanitizedData.email,
        Password: sanitizedData.password,
        FirstName: sanitizedData.firstName,
        LastName: sanitizedData.lastName,
        ApplicationName: APPLICATION_NAME,
        Token: invitationToken,
        VerificationCode: sanitizedData.invitationCode
      })
    });

    if (response.status === 429) {
      const responseText = await response.text();
      let result;
      try {
        result = JSON.parse(responseText);
        if (result.retryAfter) {
          const retryAfter = new Date(result.retryAfter);
          const waitTime = Math.ceil((retryAfter - new Date()) / 1000);
          throw new Error(`Too many requests. Please wait ${waitTime} seconds before trying again.`);
        }
      } catch (parseError) {
        throw new Error('Too many requests. Please wait a moment before trying again.');
      }
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const textResponse = await response.text();
      throw new Error(`Non-JSON response: ${response.status}. Response: ${textResponse.substring(0, 200)}`);
    }

    const result = await response.json();

    const data = result.data || result;

    if (!response.ok) {
      throw new Error(data.message || result.message || `Registration error ${response.status}`);
    }

    if (data.success === false) {
      throw new Error(data.message || "Registration failed. Please try again.");
    }

    return { success: true, message: data.message || "Account created successfully!" };
  } catch (error) {
    throw error;
  } finally {
    resetLoadingState();
  }
}

function validateRegistrationForm() {
  let isValid = true;

  $('.form-control').removeClass('is-invalid');
  $('.invalid-feedback').text('');

  const email = $('#email').val();
  if (!InputSanitizer.validateEmail(email)) {
    $('#emailError').text('Please enter a valid email address');
    $('#email').addClass('is-invalid');
    isValid = false;
  }

  const firstName = $('#firstName').val();
  const firstNameValidation = InputSanitizer.validateNameWithFeedback(firstName, 'First name');
  if (!firstNameValidation.valid) {
    $('#firstNameError').text(firstNameValidation.message);
    $('#firstName').addClass('is-invalid');
    isValid = false;
  }

  const lastName = $('#lastName').val();
  const lastNameValidation = InputSanitizer.validateNameWithFeedback(lastName, 'Last name');
  if (!lastNameValidation.valid) {
    $('#lastNameError').text(lastNameValidation.message);
    $('#lastName').addClass('is-invalid');
    isValid = false;
  }

  const password = $('#password').val();
  if (!InputSanitizer.validatePassword(password)) {
    $('#passwordError').text('Password must meet complexity requirements');
    $('#password').addClass('is-invalid');
    isValid = false;
  }

  const confirmPassword = $('#confirmPassword').val();
  if (password !== confirmPassword) {
    $('#confirmPasswordError').text('Passwords do not match');
    $('#confirmPassword').addClass('is-invalid');
    isValid = false;
  }

  const invitationCode = $('#invitationCode').val();
  if (!invitationCode || invitationCode.trim().length < 6) {
    $('#invitationCodeError').text('Please enter a valid invitation code');
    $('#invitationCode').addClass('is-invalid');
    isValid = false;
  }

  const termsAccepted = $('#termsAccepted').is(':checked');
  if (!termsAccepted) {
    $('#termsError').text('You must accept the terms and conditions');
    $('#termsAccepted').addClass('is-invalid');
    isValid = false;
  }

  // Show summary notification if validation fails
  if (!isValid) {
    NotificationSystem.showError('Please correct the highlighted fields and try again.', {
      title: 'Form Validation',
      timeout: 5000
    });
  }

  return isValid;
}

function validateFieldWithVisualFeedback(fieldSelector, errorSelector, validationFunction, errorMessage) {
  const field = $(fieldSelector);
  const errorElement = $(errorSelector);
  const isValid = validationFunction();

  if (isValid) {
    field.removeClass('is-invalid').addClass('is-valid');
    field.closest('.form-group').removeClass('has-error');
    errorElement.text('');
    return true;
  } else {
    field.removeClass('is-valid').addClass('is-invalid');
    field.closest('.form-group').addClass('has-error');
    errorElement.text(errorMessage);

    // Focus on first invalid field
    if ($('.is-invalid').length === 1) {
      field.focus();
    }

    return false;
  }
}

function initializeFormHandlers() {
  if (toggleBtns.password && toggleBtns.password.length) {
    toggleBtns.password.click(() => togglePasswordVisibility('password', toggleBtns.password));
  }
  if (toggleBtns.confirm && toggleBtns.confirm.length) {
    toggleBtns.confirm.click(() => togglePasswordVisibility('confirmPassword', toggleBtns.confirm));
  }

  regForm.submit(async function(event) {
    event.preventDefault();
    showMessage('', false);

    try {
      if (!validateRegistrationForm()) {
        return;
      }

      const userData = {
        email: $('#email').val(),
        password: $('#password').val(),
        firstName: $('#firstName').val(),
        lastName: $('#lastName').val(),
        invitationCode: $('#invitationCode').val()
      };

      const result = await registerUser(userData);

      if (result.success) {
        showMessage("Account created successfully! Redirecting to home page...", false);
        regForm[0].reset();

        setTimeout(() => {
          sessionStorage.setItem('registeredEmail', userData.email);
          sessionStorage.setItem('justRegistered', 'true');
          window.location.href = '/';
        }, 2000);
      }

    } catch (error) {
      NotificationSystem.handleError(error, {
        operation: 'registration',
        email: $('#email').val()
      });
    }
  });
}

let invitationToken = null;

function validateInvitationTokenAndExtractCode() {
  const urlParams = new URLSearchParams(window.location.search);
  invitationToken = urlParams.get('token');
  const invitationCode = urlParams.get('code');

  if (!invitationToken) {
    showUnauthorizedAccess('No invitation token provided. Please use the link from your invitation email.');
    return;
  }

  validateTokenAccess(invitationToken);

  if (invitationCode) {
    displayCodeReference(invitationCode);
  }
}

async function validateTokenAccess(token) {
  try {
    const secureUrl = SecureConfig.buildSecureUrl('InvitationService', 'validate-token');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: token
      })
    });

    const result = await response.json();
    const data = result.data || result;

    if (!data.success) {
      showUnauthorizedAccess(data.message || 'Invalid or expired invitation token.');
      return;
    }

    showTokenValidationSuccess();

  } catch (error) {
    showUnauthorizedAccess('Unable to validate invitation token. Please try again.');
  }
}

function displayCodeReference(code) {
  const codeDisplay = document.createElement('div');
  codeDisplay.className = 'alert alert-info mt-3';
  codeDisplay.innerHTML = `
    <h6>ℹ️ Your Verification Code</h6>
    <p class="mb-2">Enter this code in the form below:</p>
    <div class="code-display" style="font-family: monospace; font-size: 1.2em; font-weight: bold; color: #0066cc;">${code}</div>
    <small class="text-muted">This code was provided in your invitation email.</small>
  `;

  const form = document.getElementById('registrationForm');
  if (form && form.parentNode) {
    form.parentNode.insertBefore(codeDisplay, form);
  }
}

function showUnauthorizedAccess(message) {

  sessionStorage.setItem('invitationError', JSON.stringify({
    message: message,
    timestamp: new Date().toISOString(),
    source: 'registration-token-validation'
  }));

  const errorPageUrl = '/Invitation-Error/';

  try {
    window.location.href = errorPageUrl;
  } catch (error) {
    console.warn('Error page not found, redirecting to home:', error);
    window.location.href = '/';
  }
}

function showTokenValidationSuccess() {
  const pageHeader = document.querySelector('.card-header h3');
  if (pageHeader) {
    pageHeader.innerHTML = '✅ Create Account <small class="text-success">(Invitation Verified)</small>';
  }
}

$(document).ready(() => {
  if (!NotificationSystem.checkBrowserCompatibility()) return;

  const requiredConfig = ['functionUrl', 'applicationName'];
  if (NotificationSystem.validateConfiguration(requiredConfig)) {
    const requiredElements = ['#registrationForm', '#email', '#firstName', '#lastName'];
    if (NotificationSystem.validateDOMElements(requiredElements)) {
      initializeFormHandlers();
      validateInvitationTokenAndExtractCode();
    }
  }
});
