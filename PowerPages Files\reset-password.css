.reset-password-container {
    max-width: 600px;
}

.reset-password-container h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 2px;
    background-color: var(--primary-red);
    margin: 1rem auto;
}

#submitButton {
    background-color: var(--primary-red);
    color: var(--color-white);
    border: 2px solid var(--primary-red);
}

#submitButton:hover {
    background-color: var(--primary-red-dark);
    border-color: var(--primary-red-dark);
}

#verificationCodeGroup {
    position: relative;
}

#verificationCode {
    text-align: center;
}

#verificationCodeGroup .input-group {
    display: flex;
    position: relative;
}

#passwordForm .input-group .form-control {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

#passwordForm .input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.password-strength-indicator {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    text-align: center;
}

.password-strength-indicator.weak {
    background-color: var(--primary-red-light);
    color: var(--primary-red);
    border-left: 3px solid var(--primary-red);
}

.password-strength-indicator.medium {
    background-color: #fef3c7;
    color: #f59e0b;
    border-left: 3px solid #f59e0b;
}

.password-strength-indicator.strong {
    background-color: #f0f9ff;
    color: #059669;
    border-left: 3px solid #059669;
}

/* Notification System Integration */
.reset-password-container .notification-container {
    margin-bottom: var(--spacing-md);
}

.reset-password-container .notification {
    margin-bottom: var(--spacing-sm);
}

/* Enhanced error styling for password reuse */
.reset-password-container .notification-error {
    border-left-width: 6px;
    box-shadow: var(--shadow-md);
}

.reset-password-container .notification-error .notification-title {
    font-weight: 700;
    color: var(--primary-red);
}

/* Ensure notifications don't interfere with form layout */
.reset-password-container #passwordForm {
    margin-top: var(--spacing-sm);
}

/* Legacy notification compatibility */
.reset-password-container #errorMessage,
.reset-password-container #successMessage {
    margin-bottom: var(--spacing-md);
}

@media (max-width: 768px) {
    .reset-password-container {
        max-width: 95%;
    }

    .reset-password-container .notification {
        padding: var(--spacing-sm);
        font-size: 0.875rem;
    }

    .reset-password-container .notification-compact {
        padding: calc(var(--spacing-sm) * 0.75);
    }

}

@media (max-width: 480px) {
    .reset-password-container {
        margin: 0.5rem auto;
        padding: 1rem;
    }
    
    .reset-password-container h2 {
        font-size: 1.25rem;
    }
    
    .reset-password-container h2::after {
        width: 40px;
        margin: 0.75rem auto;
    }
    
    #verificationCode {
        font-size: 0.95rem;
        letter-spacing: 1px;
    }
}
