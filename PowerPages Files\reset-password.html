
    <meta name="azure-function-url" content="{{ settings['AzureFunctionUrl'] | default: '' }}">
    <meta name="password-function-key" content="{{ settings['Password Function Key'] | default: '' }}">
    <meta name="application-name" content="{{ settings['ApplicationName'] | default: '' }}">

    <script>
      function getConfig() {
        const functionUrl = document.querySelector('meta[name="azure-function-url"]')?.content;
        const passwordFunctionKey = document.querySelector('meta[name="password-function-key"]')?.content;
        const applicationName = document.querySelector('meta[name="application-name"]')?.content;

        return {
          functionUrl: functionUrl || null,
          passwordFunctionKey: passwordFunctionKey || null,
          applicationName: applicationName || null
        };
      }

      window.appConfig = getConfig();

  console.log("Reset Password Configuration Debug:", {
    rawAzureFunctionUrl: "{{ settings['AzureFunctionUrl'] }}",
    rawPasswordFunctionKey: "{{ settings['Password Function Key'] }}" ? "configured" : "missing",
    rawApplicationName: "{{ settings['ApplicationName'] }}",
    finalFunctionUrl: window.appConfig.functionUrl,
    finalPasswordFunctionKey: window.appConfig.passwordFunctionKey ? "configured" : "missing",
    finalApplicationName: window.appConfig.applicationName,
    allCriticalSettings: {
      AzureFunctionUrl: "{{ settings['AzureFunctionUrl'] }}",
      PasswordFunctionKey: "{{ settings['Password Function Key'] }}" ? "configured" : "missing",
      ApplicationName: "{{ settings['ApplicationName'] }}"
    }
  });


  const resetUrlParams = new URLSearchParams(window.location.search);
  const resetToken = resetUrlParams.get('token');

  console.log('🔍 HTML Token Extraction:', {
    url: window.location.href,
    search: window.location.search,
    token: resetToken ? 'Found (length: ' + resetToken.length + ')' : 'Not found'
  });


  const userEmail = resetToken ? null : ("{{ user.email }}" || null);
  const userId = resetToken ? null : ("{{ user.id }}" || null);

  window.liquidUser = {
    applicationName: window.appConfig.applicationName,
    resetToken: resetToken,
    email: userEmail,
    id: userId,
    isTokenBasedReset: !!resetToken
  };


  function sanitizeUserInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  }


  console.log("🔧 Reset Password Page Configuration Check:", {
    functionUrl: window.appConfig.functionUrl || "MISSING",
    passwordFunctionKey: window.appConfig.passwordFunctionKey ? "CONFIGURED" : "MISSING",
    applicationName: window.appConfig.applicationName || "MISSING",
    hasToken: !!resetToken,
    canBuildSecureUrl: !!(window.appConfig.functionUrl && window.appConfig.passwordFunctionKey)
  });
</script>

<div class="col-lg-12 columnBlockLayout">
<div class="row sectionBlockLayout text-start">
  <div class="container container-flex">
    <div class="container reset-password-container">
      <h2>Reset Your Password</h2>
      <p>Enter the verification code from your email and your new password.</p>

      <!-- Unified Notification Container -->
      <div id="notificationContainer" class="notification-container"></div>

      <!-- Legacy notification elements for compatibility -->
      <div id="errorMessage" class="alert alert-danger d-none"></div>
      <div id="successMessage" class="alert alert-success d-none"></div>
      
      <form id="passwordForm">
        <div class="form-group mb-3" id="verificationCodeGroup">
          <label for="verificationCode" class="form-label fw-bold">Verification Code</label>
          <div class="input-group">
            <input type="text" id="verificationCode" class="form-control" placeholder="Enter the 6-digit code from your email" maxlength="6" pattern="[0-9]{6}" autocomplete="one-time-code">
            <span class="input-group-text">🛡️</span>
          </div>
          <div class="form-text">Enter the 6-digit verification code sent to your email</div>
          <div class="invalid-feedback" id="verificationCodeError"></div>
        </div>


        <div class="form-group mb-3">
          <label for="newPassword" class="form-label fw-bold">New Password</label>
          <div class="input-group">
            <input type="password" id="newPassword" aria-describedby="passwordHelpBlock" required class="form-control" placeholder="Enter your new password" autocomplete="new-password">
            <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword" aria-label="Toggle new password visibility">
              👁️
            </button>
          </div>
          <div id="passwordHelpBlock" class="form-text">Password must be at least 8 characters, include uppercase, lowercase, numbers, special characters, and not be one of your last 12 passwords.</div>
          <div class="invalid-feedback" id="passwordError"></div>
        </div>


        <div class="form-group mb-3">
          <label for="confirmPassword" class="form-label fw-bold">Confirm New Password</label>
          <div class="input-group">
            <input type="password" id="confirmPassword" required class="form-control" placeholder="Confirm your new password" autocomplete="new-password">
            <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword" aria-label="Toggle confirm password visibility">
              👁️
            </button>
          </div>
          <div class="invalid-feedback" id="confirmPasswordError"></div>
        </div>

        <button type="submit" id="submitButton" class="btn btn-primary">Reset Password</button>

      </form>
    </div>
  </div>
</div>
</div>

<script src="reset-password.js"></script>
</body>
</html>
